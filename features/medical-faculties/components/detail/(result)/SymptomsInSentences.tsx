import { Text } from "@/components/ui/Text/Text"
import { useTranslation } from "react-i18next"
import { View } from "react-native"

export const SymptomsInSentences = () => {
    const { t } = useTranslation()

    return <View className="mt-4 gap-3 w-full">

        <Text size='body6'>
            {t('MES-1034')}
        </Text>
        <View className="px-4 py-3 rounded-lg bg-[#F9F9FC] flex-row gap-3 justify-between items-center">
            <Text size="body7" >
                đau đầu chóng mặt </Text>
            <Text size="body7" variant="subdued">
                頭痛, めまい</Text>
        </View>
    </View>
}
