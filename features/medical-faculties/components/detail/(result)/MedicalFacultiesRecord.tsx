import { Text } from "@/components/ui/Text/Text"
import { useTranslation } from "react-i18next"
import { View } from "react-native"
import { HeaderDetail } from "../HeaderDetail"
import { BoxSentences } from "./BoxSentences"
import { SymptomsInSentences } from "./SymptomsInSentences"

export const MedicalFacultiesRecord = () => {
    const { t } = useTranslation()


    return <View className="h-full">
        <HeaderDetail />

        <Text className="mt-4" size="body9" variant="error" >
            {t('MES-1033')}
        </Text>

        <BoxSentences />

        <SymptomsInSentences />
    </View>
}
