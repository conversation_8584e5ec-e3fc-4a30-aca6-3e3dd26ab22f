import { Text } from "@/components/ui/Text/Text"
import { useTranslation } from "react-i18next"
import { TouchableOpacity, View } from "react-native"

import VolumeIcon from '@/assets/icons/volume-high.svg'

import EditIcon from '@/assets/icons/edit-gray.svg'
import TrashIcon from '@/assets/icons/trash-gray.svg'
import { QuickEditor } from "./QuickEditor"

export const BoxSentences = () => {
    const { t } = useTranslation()


    return <View className="mt-4 gap-3 w-full">

        {/* Tiếng việt */}
        <View className="flex-row gap-2 justify-between items-center">
            <Text size='body6'>
                {t('MES-46')}
            </Text>

            <TouchableOpacity>
                <VolumeIcon className="size-5" />
            </TouchableOpacity>
        </View>
        <View className="p-4 gap-4 bg-[#F9F9FC] rounded-lg">
            <Text size="body7" >
                Ch<PERSON><PERSON> b<PERSON><PERSON> s<PERSON>, tôi bị đau đầu chóng mặt từ hôm qua, tình trạng nghiêm trọng hơn khi đi lên xuống cầu thang. Đ<PERSON> nhiều ở hai bên thái dương. </Text>

            <View className="flex-row justify-between gap-2 items-center">

                <QuickEditor />
                <View className="flex-row gap-3 items-center">
                    <TouchableOpacity>
                        <EditIcon className="size-5" />
                    </TouchableOpacity>
                    <TouchableOpacity>
                        <TrashIcon className="size-5" />
                    </TouchableOpacity>
                </View>
            </View>
        </View>

        {/* Tiếng nhật */}

        <View className="flex-row gap-2 justify-between items-center">
            <Text size='body6'>
                {t('MES-47')}
            </Text>

            <TouchableOpacity>
                <VolumeIcon className="size-5" />
            </TouchableOpacity>
        </View>
        <View className="p-4 gap-4 bg-[#F9F9FC] rounded-lg">
            <Text size="body7" >
                こんにちは、昨日から頭痛とめまいがあります。階段の上り下りで症状が悪化します。こめかみが特に痛みます。
            </Text>


        </View>
    </View>
}
