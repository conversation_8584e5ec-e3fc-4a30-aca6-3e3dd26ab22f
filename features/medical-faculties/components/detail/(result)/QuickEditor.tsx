import StarIcon from '@/assets/icons/star-fly.svg'
import { Text } from "@/components/ui/Text/Text"
import { useState } from 'react'
import { useTranslation } from "react-i18next"
import { Modal, TouchableOpacity, View } from "react-native"
import { SafeAreaView } from 'react-native-safe-area-context'

import ArrowLeftIcon from '@/assets/icons/arrow-left.svg'

export const QuickEditor = () => {
    const { t } = useTranslation()

    const [isVisible, setIsVisible] = useState(false)

    return <View>
        <TouchableOpacity onPress={(() => setIsVisible(true))} className="flex-row gap-1 items-center py-1 px-2 rounded-md bg-[##DBEAFE]">
            <StarIcon />
            <Text size="body8" variant="primary">
                {t('MES-1035')}
            </Text>
        </TouchableOpacity>

        <Modal
            visible={isVisible}
            presentationStyle="fullScreen"
            animationType="slide"
        >
            <SafeAreaView style={{ flex: 1 }} edges={['top', 'left', 'right', 'bottom']}>
                <View style={{ flex: 1, padding: 16 }}>
                    <View className='flex-row gap-2 justify-between items-center'>
                        <TouchableOpacity onPress={() => setIsVisible(false)}>
                            <ArrowLeftIcon className='size-6' />
                        </TouchableOpacity>

                        <Text size="body3" variant="primary">
                            {t('MES-1036')}
                        </Text>

                        <View className='size-6'></View>
                    </View>
                </View>
            </SafeAreaView>
        </Modal>
    </View>

}